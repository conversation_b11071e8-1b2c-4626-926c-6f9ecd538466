// Include any global setup for the Jest testing environment
require('@testing-library/jest-native/extend-expect');

// Fix for React Native StatusBar clearImmediate issue in jsdom
global.setImmediate = global.setImmediate || ((fn, ...args) => global.setTimeout(fn, 0, ...args));
global.clearImmediate = global.clearImmediate || global.clearTimeout;

// Mock the Expo modules that might cause issues in the testing environment
jest.mock('expo-font');
jest.mock('expo-asset');
jest.mock('expo-linear-gradient');

// Mock expo-router
jest.mock('expo-router', () => {
  const React = require('react');
  const { Text, Pressable } = require('react-native');

  const Link = React.forwardRef(({ children, onPress, ...props }, ref) => (
    React.createElement(Pressable, { onPress, ...props, ref },
      React.createElement(Text, {}, children)
    )
  ));

  return {
    Link,
    useRouter: () => ({
      push: jest.fn(),
      back: jest.fn(),
      replace: jest.fn(),
    }),
    useLocalSearchParams: () => ({}),
    useGlobalSearchParams: () => ({}),
    useSegments: () => [],
    router: {
      push: jest.fn(),
      replace: jest.fn(),
      back: jest.fn(),
    },
  };
});

// Mock React Native components that might cause issues
jest.mock('react-native-vector-icons');

// Mock StatusBar to prevent clearImmediate issues
jest.mock('react-native/Libraries/Components/StatusBar/StatusBar', () => {
  const React = require('react');
  const { View } = require('react-native');

  const StatusBar = React.forwardRef((props, ref) => {
    return React.createElement(View, { ...props, ref });
  });

  StatusBar.setBarStyle = jest.fn();
  StatusBar.setBackgroundColor = jest.fn();
  StatusBar.setHidden = jest.fn();
  StatusBar.setTranslucent = jest.fn();
  StatusBar.setNetworkActivityIndicatorVisible = jest.fn();
  StatusBar.pushStackEntry = jest.fn();
  StatusBar.popStackEntry = jest.fn();
  StatusBar.replaceStackEntry = jest.fn();

  return StatusBar;
});

// Mock constants - fix the Colors export structure
jest.mock('./constants/Colors', () => ({
  default: {
    light: {
      text: '#000',
      background: '#fff',
      tint: '#2f95dc',
      tabIconDefault: '#ccc',
      tabIconSelected: '#2f95dc',
    },
    dark: {
      text: '#fff',
      background: '#000',
      tint: '#fff',
      tabIconDefault: '#ccc',
      tabIconSelected: '#fff',
    },
  },
}));

// Mock Animated components to prevent animation warnings
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');

  // Mock Animated to prevent act() warnings
  RN.Animated.timing = jest.fn(() => ({
    start: jest.fn((callback) => callback && callback()),
    stop: jest.fn(),
    reset: jest.fn(),
  }));

  RN.Animated.spring = jest.fn(() => ({
    start: jest.fn((callback) => callback && callback()),
    stop: jest.fn(),
    reset: jest.fn(),
  }));

  RN.Animated.decay = jest.fn(() => ({
    start: jest.fn((callback) => callback && callback()),
    stop: jest.fn(),
    reset: jest.fn(),
  }));

  return RN;
});

// Mock useColorScheme hook
jest.mock('./components/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

// Mock expo-web-browser
jest.mock('expo-web-browser', () => ({
  openBrowserAsync: jest.fn(),
}));

// This enables proper error messages for React tests
globalThis.ErrorUtils = {
  setGlobalHandler: jest.fn(),
};
